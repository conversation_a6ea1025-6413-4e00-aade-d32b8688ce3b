// src/types/chat.ts
export interface Message {
    id: string;
    role: "user" | "assistant";
    content: string;
    timestamp: Date;
}

export interface ChatState {
    messages: Message[];
    isLoading: boolean;
    error: string | null;
}

export interface MessageStyles {
    container: string;
    bubble: string;
    avatar: string;
    avatarFallback: string;
}

export interface ChatConfig {
    maxMessageLength: number;
    maxMessages: number;
    retryAttempts: number;
    scrollBehavior: ScrollBehavior;
}
