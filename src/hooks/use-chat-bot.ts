// src/hooks/use-chat-bot.ts
import { useState, useRef, useEffect, FormEvent } from "react";
import { useToast } from "@/hooks/use-toast";
import { handleQuery } from "@/app/actions";

/**
 * Type representing a single chat message.
 */
type Role = "user" | "assistant";

export interface Message {
    role: Role;
    content: string;
}

/**
 * Parameters accepted by the hook – currently only the toast function,
 * but the shape allows easy extension later.
 */
interface UseChatBotOptions {
    toast: ReturnType<typeof useToast>["toast"];
}

/**
 * Initial bot greeting – extracted to a constant for easy localisation.
 */
const INITIAL_MESSAGES: Message[] = [
    {
        role: "assistant",
        content:
            "Hello! I'm your BaliBlissed travel assistant. How can I help you plan your trip to Bali today?",
    },
];

/**
 * Custom hook that centralises all chat‑related state and behaviour.
 * It returns everything the component needs to render and interact.
 */
export function useChatBot({ toast }: UseChatBotOptions) {
    const [messages, setMessages] = useState<Message[]>(INITIAL_MESSAGES);
    const [input, setInput] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const scrollAreaRef = useRef<HTMLDivElement>(null);

    /**
     * Scroll to the bottom of the chat whenever messages or loading state change.
     * Respects user’s reduced‑motion preference by avoiding smooth scroll.
     */
    useEffect(() => {
        const el = scrollAreaRef.current?.querySelector("div");
        if (!el) return;

        const prefersReducedMotion =
            typeof window !== "undefined" &&
            window.matchMedia("(prefers-reduced-motion: reduce)").matches;

        if (prefersReducedMotion) {
            el.scrollTop = el.scrollHeight;
        } else {
            el.scrollTo({
                top: el.scrollHeight,
                behavior: "smooth",
            });
        }
    }, [messages, isLoading]);

    /**
     * Handles form submission – sends the user query to the server and updates UI.
     * Includes robust error handling and toast notifications.
     */
    const handleSubmit = async (e: FormEvent): Promise<void> => {
        e.preventDefault();

        // Guard against empty submissions or double‑clicks.
        if (!input.trim() || isLoading) return;

        const userMessage: Message = { role: "user", content: input };
        setMessages((prev) => [...prev, userMessage]);
        setInput("");
        setIsLoading(true);

        try {
            const response = await handleQuery({ query: userMessage.content });

            if (response.success && response.data) {
                const assistantMessage: Message = {
                    role: "assistant",
                    content: response.data.answer,
                };
                setMessages((prev) => [...prev, assistantMessage]);
            } else {
                throw new Error(response.error ?? "Unknown error");
            }
        } catch (error: unknown) {
            // Show a generic fallback message in the UI.
            const errorMessage: Message = {
                role: "assistant",
                content: "I'm sorry, I encountered an error. Please try again.",
            };
            setMessages((prev) => [...prev, errorMessage]);

            // Notify the user via toast – keeps the UI non‑blocking.
            toast({
                variant: "destructive",
                title: "AI Error",
                description:
                    error instanceof Error ? error.message : `${error}`,
            });
        } finally {
            setIsLoading(false);
        }
    };

    return {
        messages,
        input,
        setInput,
        isLoading,
        scrollAreaRef,
        handleSubmit,
    };
}
