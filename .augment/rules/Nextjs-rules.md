---
type: "always_apply"
---

Update to improve performance, accessibility, maintainable, error handling, add memoization where possible, add meaninful comments, respect reduce motion when animating import useReducedMotion from "@/hooks/use-reduced-motion", and for best practices. Make sure no to break any functionalities and layout setup. Extract any values possible to constants. create a hook file if there is a chance to do so. Always follow Next.js best practices, linting errors, and type errors. Whenever proposing a new or updated file use the markdown code block syntax and always add a file path in a comment on the top of the code block. Please show me the full code of the changed files.